import 'package:flutter/material.dart';
import 'package:sf_app/core/constants/enums.dart';
import 'package:sf_app/core/theme/color_pallette.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/features/market_v2/domain/models/stock_response.dart';
import 'package:sf_app/flavors.dart';
// import 'package:sf_app/flavors.dart';
import '../config/app_config.dart';
import '../constants/string_constants.dart';

String? getInstrumentId(StockItem? data) {
  if (data == null ||
      data.market == null ||
      data.securityType == null ||
      data.symbol == null) {
    return null;
  }
  return '${data.market}|${data.securityType}|${data.symbol}';
}

String? getSocketId(StockItem? data) {
  if (data == null ||
      data.market == null ||
      data.securityType == null ||
      data.symbol == null) {
    return null;
  }
  return '${data.market}|${data.securityType}|Q|${data.symbol}|R';
}

Color getStatusColor(int status) => switch (status) {
  0 => const Color(0xFF1677FF), // Auditing (Blue)
  1 => const Color(0xFF52C41A), // Progress (Green) - Not used
  2 => const Color(0xFFFF4D4F), // Rejected (Red)
  3 => const Color(0xFFFAAD14), // In Process (Orange)
  4 => const Color(0xFF52C41A), // Completed (Green)
  6 => const Color(0xFFFF4D4F), // Cancelled (Red)
  _ => const Color(0xFF1677FF), // Default (Blue)
};

String getStatusText(int status) => switch (status) {
  0 => StringConstants.auditing,
  1 => StringConstants.progress, // Not used
  2 => StringConstants.rejected,
  3 => StringConstants.inProcess,
  4 => StringConstants.completed,
  6 => StringConstants.canceled,
  _ => '--'
};

extension NumColorExtension on num {
  Color getValueColor(BuildContext context,
      {Color? greenColor, Color? redColor}) {
    if (this == 0)  return myColorScheme(context).subTitleColor ?? ColorPalette.subTitleColor;
    // final marketColor = F.appFlavor == Flavor.sis
    //     ? MarketColor.redUpGreenDown
    //     : MarketColor.greenUpRedDown;
    final marketColor = AppConfig.instance.marketColor;
    return marketColor == MarketColor.redUpGreenDown
        ? this <= 0
            ? greenColor ?? Colors.green.shade600
            : redColor ?? Colors.red.shade600
        : this <= 0
            ? redColor ?? Colors.red.shade600
            : greenColor ?? Colors.green.shade600;
  }
}

bool get isSisFlavor => F.appFlavor == Flavor.sis;
// bool get isCforexFlavor => F.appFlavor == Flavor.cfroex;
bool get isSfFlavor => F.appFlavor == Flavor.sf_app;

