import 'package:flutter/material.dart';
import 'package:sf_app/core/theme/color_pallette.dart';

MyColorScheme myColorScheme(BuildContext context) {
  return Theme.of(context).extension<MyColorScheme>()!;
}

@immutable
class MyColorScheme extends ThemeExtension<MyColorScheme> {
  const MyColorScheme({
    this.primaryColor,
    this.primaryVar1,
    this.primaryVar2,
    this.primaryVar3,
    this.textFieldBorderColor,
    this.feeColor,
    this.iconBackgroundColor,
    this.iconBackgroundColor2,
    this.primaryVar4,
    this.iconColor,
    this.shimmerColor,
    this.shadowColor,
    this.secondaryColor,
    this.backgroundColor,
    this.white,
    this.secondaryVar1,
    this.primaryBlack,
    this.successColor,
    this.greyColor1,
    this.greyColor2,
    this.greyColor3,
    this.greyColor4,
    this.greyColor5,
    this.greyColor6,
    this.lightGrey,
    this.lightGrey4,
    this.trc,
    this.lightGrey3,
    this.lightGrey2,
    this.black,
    this.labelColor,
    this.titleColor,
    this.dropShadow,
    this.skeletonColor,
    this.borderColor,
    this.borderColor2,
    this.deniedColor,
    this.subTitleColor,
    this.pendingColor,
    this.greenColor,
    this.backgroundColor1,
    this.tagBlue,
    this.tagGreen,
    this.tagRed,
    this.cardColor,
    this.viewAllColor,
    this.appBarIconColor,
    this.tableHeaderColor,
    this.buttonColorDisabled,
    this.borderColor3,
    this.backgroundColor2,
    this.chatSubtitle,
    this.chatDividerColor,
    this.subTitleColor2,
    this.cardColor2,
    this.cardColor3,
  });

  final Color? primaryColor,
      primaryVar1,
      primaryVar2,
      primaryVar3,
      textFieldBorderColor,
      feeColor,
      iconBackgroundColor,
      iconBackgroundColor2,
      primaryVar4,
      iconColor,
      shimmerColor,
      shadowColor,
      secondaryColor,
      backgroundColor,
      white,
      secondaryVar1,
      primaryBlack,
      successColor,
      greyColor1,
      greyColor2,
      greyColor3,
      greyColor4,
      greyColor5,
      greyColor6,
      lightGrey,
      lightGrey4,
      trc,
      lightGrey3,
      lightGrey2,
      black,
      labelColor,
      titleColor,
      dropShadow,
      skeletonColor,
      borderColor,
      borderColor2,
      deniedColor,
      subTitleColor,
      pendingColor,
      greenColor,
      backgroundColor1,
      tagBlue,
      tagGreen,
      tagRed,
      cardColor,
      viewAllColor,
      appBarIconColor,
      tableHeaderColor,
      buttonColorDisabled,
      borderColor3,
      backgroundColor2,
      chatSubtitle,
      chatDividerColor,
      subTitleColor2,
      cardColor2,
      cardColor3;

  @override
  ThemeExtension<MyColorScheme> copyWith({
    Color? primaryColor,
    Color? primaryVar1,
    Color? primaryVar2,
    Color? primaryVar3,
    Color? textFieldBorderColor,
    Color? feeColor,
    Color? iconBackgroundColor,
    Color? iconBackgroundColor2,
    Color? primaryVar4,
    Color? iconColor,
    Color? shimmerColor,
    Color? shadowColor,
    Color? secondaryColor,
    Color? backgroundColor,
    Color? white,
    Color? secondaryVar1,
    Color? primaryBlack,
    Color? successColor,
    Color? greyColor1,
    Color? greyColor2,
    Color? greyColor3,
    Color? greyColor4,
    Color? greyColor5,
    Color? greyColor6,
    Color? lightGrey,
    Color? lightGrey4,
    Color? trc,
    Color? lightGrey3,
    Color? lightGrey2,
    Color? black,
    Color? labelColor,
    Color? titleColor,
    Color? dropShadow,
    Color? skeletonColor,
    Color? borderColor,
    Color? borderColor2,
    Color? deniedColor,
    Color? subTitleColor,
    Color? pendingColor,
    Color? greenColor,
    Color? backgroundColor1,
    Color? tagBlue,
    Color? tagGreen,
    Color? tagRed,
    Color? cardColor,
    Color? viewAllColor,
    Color? appBarIconColor,
    Color? tableHeaderColor,
    Color? buttonColorDisabled,
    Color? borderColor3,
    Color? backgroundColor2,
    Color? chatSubtitle,
    Color? chatDividerColor,
    Color? subTitleColor2,
    Color? cardColor2,
    Color? cardColor3,
  }) {
    return MyColorScheme(
      primaryColor: primaryColor ?? this.primaryColor,
      primaryVar1: primaryVar1 ?? this.primaryVar1,
      primaryVar2: primaryVar2 ?? this.primaryVar2,
      primaryVar3: primaryVar3 ?? this.primaryVar3,
      textFieldBorderColor: textFieldBorderColor ?? this.textFieldBorderColor,
      feeColor: feeColor ?? this.feeColor,
      iconBackgroundColor: iconBackgroundColor ?? this.iconBackgroundColor,
      iconBackgroundColor2: iconBackgroundColor2 ?? this.iconBackgroundColor2,
      primaryVar4: primaryVar4 ?? this.primaryVar4,
      iconColor: iconColor ?? this.iconColor,
      shimmerColor: shimmerColor ?? this.shimmerColor,
      shadowColor: shadowColor ?? this.shadowColor,
      secondaryColor: secondaryColor ?? this.secondaryColor,
      backgroundColor: backgroundColor ?? this.backgroundColor,
      white: white ?? this.white,
      secondaryVar1: secondaryVar1 ?? this.secondaryVar1,
      primaryBlack: primaryBlack ?? this.primaryBlack,
      successColor: successColor ?? this.successColor,
      greyColor1: greyColor1 ?? this.greyColor1,
      greyColor2: greyColor2 ?? this.greyColor2,
      greyColor3: greyColor3 ?? this.greyColor3,
      greyColor4: greyColor4 ?? this.greyColor4,
      greyColor5: greyColor5 ?? this.greyColor5,
      greyColor6: greyColor6 ?? this.greyColor6,
      lightGrey: lightGrey ?? this.lightGrey,
      lightGrey4: lightGrey4 ?? this.lightGrey4,
      trc: trc ?? this.trc,
      lightGrey3: lightGrey3 ?? this.lightGrey3,
      lightGrey2: lightGrey2 ?? this.lightGrey2,
      black: black ?? this.black,
      labelColor: labelColor ?? this.labelColor,
      titleColor: titleColor ?? this.titleColor,
      dropShadow: dropShadow ?? this.dropShadow,
      skeletonColor: skeletonColor ?? this.skeletonColor,
      borderColor: borderColor ?? this.borderColor,
      borderColor2: borderColor2 ?? this.borderColor2,
      deniedColor: deniedColor ?? this.deniedColor,
      subTitleColor: subTitleColor ?? this.subTitleColor,
      pendingColor: pendingColor ?? this.pendingColor,
      greenColor: greenColor ?? this.greenColor,
      backgroundColor1: backgroundColor1 ?? this.backgroundColor1,
      tagBlue: tagBlue ?? this.tagBlue,
      tagGreen: tagGreen ?? this.tagGreen,
      cardColor: cardColor ?? this.cardColor,
      viewAllColor: viewAllColor ?? this.viewAllColor,
      appBarIconColor: appBarIconColor ?? this.appBarIconColor,
      tableHeaderColor: tableHeaderColor ?? this.tableHeaderColor,
      buttonColorDisabled: buttonColorDisabled ?? this.buttonColorDisabled,
      borderColor3: borderColor3 ?? this.borderColor3,
      backgroundColor2: backgroundColor2 ?? this.backgroundColor2,
      chatSubtitle: chatSubtitle ?? this.chatSubtitle,
      chatDividerColor: chatDividerColor ?? this.chatDividerColor,
      subTitleColor2: subTitleColor2 ?? this.subTitleColor2,
      cardColor2: cardColor2 ?? this.cardColor2,
      cardColor3: cardColor3 ?? this.cardColor3,
    );
  }

  @override
  ThemeExtension<MyColorScheme> lerp(
    covariant ThemeExtension<MyColorScheme>? other,
    double t,
  ) {
    if (other is! MyColorScheme) {
      return this;
    }

    return MyColorScheme(
      primaryColor: Color.lerp(primaryColor, other.primaryColor, t),
      primaryVar1: Color.lerp(primaryVar1, other.primaryVar1, t),
      primaryVar2: Color.lerp(primaryVar2, other.primaryVar2, t),
      primaryVar3: Color.lerp(primaryVar3, other.primaryVar3, t),
      textFieldBorderColor: Color.lerp(textFieldBorderColor, other.textFieldBorderColor, t),
      feeColor: Color.lerp(feeColor, other.feeColor, t),
      iconBackgroundColor: Color.lerp(iconBackgroundColor, other.iconBackgroundColor, t),
      iconBackgroundColor2: Color.lerp(iconBackgroundColor2, other.iconBackgroundColor2, t),
      primaryVar4: Color.lerp(primaryVar4, other.primaryVar4, t),
      iconColor: Color.lerp(iconColor, other.iconColor, t),
      shimmerColor: Color.lerp(shimmerColor, other.shimmerColor, t),
      shadowColor: Color.lerp(shadowColor, other.shadowColor, t),
      secondaryColor: Color.lerp(secondaryColor, other.secondaryColor, t),
      backgroundColor: Color.lerp(backgroundColor, other.backgroundColor, t),
      white: Color.lerp(white, other.white, t),
      secondaryVar1: Color.lerp(secondaryVar1, other.secondaryVar1, t),
      primaryBlack: Color.lerp(primaryBlack, other.primaryBlack, t),
      successColor: Color.lerp(successColor, other.successColor, t),
      greyColor1: Color.lerp(greyColor1, other.greyColor1, t),
      greyColor2: Color.lerp(greyColor2, other.greyColor2, t),
      greyColor3: Color.lerp(greyColor3, other.greyColor3, t),
      greyColor4: Color.lerp(greyColor4, other.greyColor4, t),
      greyColor5: Color.lerp(greyColor5, other.greyColor5, t),
      greyColor6: Color.lerp(greyColor6, other.greyColor6, t),
      lightGrey: Color.lerp(lightGrey, other.lightGrey, t),
      lightGrey4: Color.lerp(lightGrey4, other.lightGrey4, t),
      trc: Color.lerp(trc, other.trc, t),
      lightGrey3: Color.lerp(lightGrey3, other.lightGrey3, t),
      lightGrey2: Color.lerp(lightGrey2, other.lightGrey2, t),
      black: Color.lerp(black, other.black, t),
      labelColor: Color.lerp(labelColor, other.labelColor, t),
      titleColor: Color.lerp(titleColor, other.titleColor, t),
      dropShadow: Color.lerp(dropShadow, other.dropShadow, t),
      skeletonColor: Color.lerp(skeletonColor, other.skeletonColor, t),
      borderColor: Color.lerp(borderColor, other.borderColor, t),
      borderColor2: Color.lerp(borderColor2, other.borderColor2, t),
      deniedColor: Color.lerp(deniedColor, other.deniedColor, t),
      subTitleColor: Color.lerp(subTitleColor, other.subTitleColor, t),
      pendingColor: Color.lerp(pendingColor, other.pendingColor, t),
      greenColor: Color.lerp(greenColor, other.greenColor, t),
      backgroundColor1: Color.lerp(backgroundColor1, other.backgroundColor1, t),
      tagBlue: Color.lerp(tagBlue, other.tagBlue, t),
      tagGreen: Color.lerp(tagGreen, other.tagGreen, t),
      tagRed: Color.lerp(tagRed, other.tagRed, t),
      cardColor: Color.lerp(cardColor, other.cardColor, t),
      viewAllColor: Color.lerp(viewAllColor, other.viewAllColor, t),
      appBarIconColor: Color.lerp(appBarIconColor, other.appBarIconColor, t),
      tableHeaderColor: Color.lerp(tableHeaderColor, other.tableHeaderColor, t),
      buttonColorDisabled: Color.lerp(buttonColorDisabled, other.buttonColorDisabled, t),
      borderColor3: Color.lerp(borderColor3, other.borderColor3, t),
      backgroundColor2: Color.lerp(backgroundColor2, other.backgroundColor2, t),
      chatSubtitle: Color.lerp(chatSubtitle, other.chatSubtitle, t),
      chatDividerColor: Color.lerp(chatDividerColor, other.chatDividerColor, t),
      subTitleColor2: Color.lerp(subTitleColor2, other.subTitleColor2, t),
      cardColor2: Color.lerp(cardColor2, other.cardColor2, t),
      cardColor3: Color.lerp(cardColor3, other.cardColor3, t),
    );
  }

  @override
  String toString() {
    return '''
      MyColorScheme(
      primaryColor: $primaryColor,
      primaryVar1: $primaryVar1,
      primaryVar2: $primaryVar2,
      primaryVar3: $primaryVar3,
      textFieldBorderColor: $textFieldBorderColor,
      feeColor: $feeColor,
      iconBackgroundColor: $iconBackgroundColor,
      iconBackgroundColor2: $iconBackgroundColor2,
      primaryVar4: $primaryVar4,
      iconColor: $iconColor,
      shimmerColor: $shimmerColor,
      shadowColor: $shadowColor,
      secondaryColor: $secondaryColor,
      backgroundColor: $backgroundColor,
      white: $white,
      secondaryVar1: $secondaryVar1,
      primaryBlack: $primaryBlack,
      successColor: $successColor,
      greyColor1: $greyColor1,
      greyColor2: $greyColor2,
      greyColor3: $greyColor3,
      greyColor4: $greyColor4,
      greyColor5: $greyColor5,
      greyColor6: $greyColor6,
      lightGrey: $lightGrey,
      lightGrey4: $lightGrey4,
      trc: $trc,
      lightGrey3: $lightGrey3,
      lightGrey2: $lightGrey2,
      black: $black,
      labelColor: $labelColor,
      titleColor: $titleColor,
      dropShadow: $dropShadow,
      skeletonColor: $skeletonColor,
      borderColor: $borderColor,
      borderColor2: $borderColor2,
      deniedColor: $deniedColor,
      subTitleColor: $subTitleColor,
      pendingColor: $pendingColor,
      greenColor: $greenColor,
      backgroundColor1: $backgroundColor1,
      tagBlue: $tagBlue,
      tagGreen: $tagGreen,
      tagRed: $tagRed,
      cardColor: $cardColor,
      viewAllColor: $viewAllColor,
      appBarIconColor: $appBarIconColor,
      tableHeaderColor: $tableHeaderColor,
      buttonColorDisabled: $buttonColorDisabled,
      borderColor3: $borderColor3,
      backgroundColor2: $backgroundColor2,
      chatSubtitle: $chatSubtitle,
      chatDividerColor: $chatDividerColor,
      subTitleColor2: $subTitleColor2,
      cardColor2: $cardColor2,
      cardColor3: $cardColor3,
      )''';
  }

  // Define lightScheme and darkScheme with the new color names
  static MyColorScheme lightScheme = MyColorScheme(
    primaryColor: ColorPalette.primaryColor,
    primaryVar1: ColorPalette.primaryVar1,
    primaryVar2: ColorPalette.primaryVar2,
    primaryVar3: ColorPalette.primaryVar3,
    textFieldBorderColor: ColorPalette.textFieldBorderColor,
    feeColor: ColorPalette.feeColor,
    iconBackgroundColor: ColorPalette.iconBackgroundColor,
    iconBackgroundColor2: ColorPalette.iconBackgroundColor2,
    primaryVar4: ColorPalette.primaryVar4,
    iconColor: ColorPalette.iconColor,
    shimmerColor: ColorPalette.shimmerColor,
    shadowColor: ColorPalette.shadowColor,
    secondaryColor: ColorPalette.secondaryColor,
    backgroundColor: ColorPalette.backgroundColor,
    white: ColorPalette.white,
    secondaryVar1: ColorPalette.secondaryVar1,
    primaryBlack: ColorPalette.primaryBlack,
    successColor: ColorPalette.successColor,
    greyColor1: ColorPalette.greyColor1,
    greyColor2: ColorPalette.greyColor2,
    greyColor3: ColorPalette.greyColor3,
    greyColor4: ColorPalette.greyColor4,
    greyColor5: ColorPalette.greyColor5,
    greyColor6: ColorPalette.greyColor6,
    lightGrey: ColorPalette.lightGrey,
    lightGrey4: ColorPalette.lightGrey4,
    trc: ColorPalette.trc,
    lightGrey3: ColorPalette.lightGrey3,
    lightGrey2: ColorPalette.lightGrey2,
    black: ColorPalette.black,
    labelColor: ColorPalette.labelColor,
    titleColor: ColorPalette.titleColor,
    dropShadow: ColorPalette.dropShadow,
    skeletonColor: ColorPalette.skeletonColor,
    borderColor: ColorPalette.borderColor,
    borderColor2: ColorPalette.borderColor2,
    deniedColor: ColorPalette.deniedColor,
    subTitleColor: ColorPalette.subTitleColor,
    pendingColor: ColorPalette.pendingColor,
    greenColor: ColorPalette.greenColor,
    backgroundColor1: ColorPalette.backgroundColor1,
    tagBlue: ColorPalette.tagBlue,
    tagGreen: ColorPalette.tagGreen,
    tagRed: ColorPalette.tagRed,
    cardColor: ColorPalette.cardColor,
    viewAllColor: ColorPalette.viewAllColor,
    appBarIconColor: ColorPalette.appBarIconColor,
    tableHeaderColor: ColorPalette.tableHeaderColor,
    buttonColorDisabled: ColorPalette.buttonColorDisabled,
    borderColor3: ColorPalette.borderColor3,
    backgroundColor2: ColorPalette.backgroundColor2,
    chatSubtitle: ColorPalette.chatSubtitle,
    chatDividerColor: ColorPalette.chatDividerColor,
    subTitleColor2: ColorPalette.subTitleColor2,
    cardColor2: ColorPalette.cardColor2,
    cardColor3: ColorPalette.cardColor3,
  );

  static MyColorScheme darkScheme = MyColorScheme(
    primaryColor: ColorPalette.primaryColorDark,
    titleColor: ColorPalette.titleColorDark,
    primaryVar1: ColorPalette.primaryVar1,
    primaryVar2: ColorPalette.primaryVar2,
    primaryVar3: ColorPalette.primaryVar3,
    textFieldBorderColor: ColorPalette.textFieldBorderColorDark,
    feeColor: ColorPalette.feeColorDark,
    iconBackgroundColor: ColorPalette.iconBackgroundColorDark,
    iconBackgroundColor2: ColorPalette.iconBackgroundColor2Dark,
    primaryVar4: ColorPalette.primaryVar4Dark,
    iconColor: ColorPalette.iconColorDark,
    shimmerColor: ColorPalette.shimmerColorDark,
    shadowColor: ColorPalette.shadowColorDark,
    secondaryColor: ColorPalette.secondaryColorDark,
    backgroundColor: ColorPalette.backgroundColorDark,
    white: ColorPalette.whiteDark,
    secondaryVar1: ColorPalette.secondaryVar1Dark,
    primaryBlack: ColorPalette.primaryBlackDark,
    successColor: ColorPalette.successColorDark,
    greyColor1: ColorPalette.greyColor1Dark,
    greyColor2: ColorPalette.greyColor2Dark,
    greyColor3: ColorPalette.greyColor3Dark,
    greyColor4: ColorPalette.greyColor4Dark,
    greyColor5: ColorPalette.greyColor5Dark,
    greyColor6: ColorPalette.greyColor6Dark,
    lightGrey: ColorPalette.lightGreyDark,
    lightGrey4: ColorPalette.lightGrey4Dark,
    trc: ColorPalette.trcDark,
    lightGrey3: ColorPalette.lightGrey3Dark,
    lightGrey2: ColorPalette.lightGrey2Dark,
    black: ColorPalette.blackDark,
    labelColor: ColorPalette.labelColorDark,
    dropShadow: ColorPalette.dropShadowDark,
    skeletonColor: ColorPalette.skeletonColorDark,
    borderColor: ColorPalette.borderColorDark,
    borderColor2: ColorPalette.borderColor2Dark,
    deniedColor: ColorPalette.deniedColorDark,
    subTitleColor: ColorPalette.subTitleColorDark,
    pendingColor: ColorPalette.pendingColorDark,
    greenColor: ColorPalette.greenColorDark,
    backgroundColor1: ColorPalette.backgroundColor1Dark,
    tagBlue: ColorPalette.tagBlueDark,
    tagGreen: ColorPalette.tagGreenDark,
    tagRed: ColorPalette.tagRedDark,
    cardColor: ColorPalette.cardColorDark,
    viewAllColor: ColorPalette.viewAllColorDark,
    appBarIconColor: ColorPalette.appBarIconColorDark,
    tableHeaderColor: ColorPalette.tableHeaderColorDark,
    buttonColorDisabled: ColorPalette.buttonColorDisabledDark,
    borderColor3: ColorPalette.borderColor3Dark,
    backgroundColor2: ColorPalette.backgroundColor2Dark,
    chatSubtitle: ColorPalette.chatSubtitleDark,
    chatDividerColor: ColorPalette.chatDividerColorDark,
    subTitleColor2: ColorPalette.subTitleColor2Dark,
    cardColor2: ColorPalette.cardColor2Dark,
    cardColor3: ColorPalette.cardColor3Dark,
  );
}
