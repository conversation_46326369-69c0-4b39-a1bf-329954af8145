import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';

import '../../features/smart_investment/domain/models/order_rate/order_rate.dart';
import '../../features/smart_investment/domain/models/purchase_percentage/purchase_percentage.dart';
import '../../features/wallet/deposit/domain/models/wallet_coin/wallet_coin.dart';
import '../../flavors.dart';
import '../constants/app_constants.dart';
import '../constants/enums.dart';
import '../constants/keys.dart';
import '../dependency_injection/injectable.dart';
import '../models/app_config/payment_type_model.dart';
import '../models/Locale/locale_model.dart';
import '../services/language/language_repository.dart';
import '../utils/shared_preference_helper.dart';
import 'payment_config.dart';
import 'purchase_percentage_config.dart';

/// Configuration class for app-wide settings.
///
/// This class follows the [singleton] pattern to ensure a single instance is shared across the app.
/// It contains configuration settings that vary based on the app flavor ([SIS], [CFROEX], [SF_APP] ,[NCM]).
/// [MarketColor] is used to determine the color of the market price.
/// The configuration includes:
/// * App metadata (name, URLs, assets)
/// * Feature flags
/// * Locale settings
/// * Payment configurations
///
class AppConfig {
  /// The app flavor ([SIS], [CFROEX], [SF_APP] ,[NCM])
  final Flavor flavor;

  /// The [display name]of the app
  final String appName;

  /// [Base URL] for API calls
  final String baseUrl;

  /// [WebSocket URL] for market data
  final String marketWsUrl;

  /// [Public URL] of the web app
  final String appUrl;

  /// [icon] Path to the app icon asset
  final String icon;

  /// [introVideo] Path to the intro video asset
  final String introVideo;

  /// [showDebugVersionTag] Whether to show debug version tag
  final bool showDebugVersionTag;

  /// Whether to fetch community records
  final bool fetchCommunityRecords;

  /// Whether to show trading wallet features
  final bool showTradingWallet;

  /// Whether to show transfer preview
  final bool showTransferPreview;

  /// Whether to show benefit rules
  final bool showBenefitRules;

  /// Whether to show transfer functionality
  final bool showTransfer;

  /// Whether to show smart investment purchase percentage
  final bool showSmartInvestmentPurchasePercentage;

  /// The type of account ('1' for SIS, '3' for others)
  final String accountType;

  /// Whether to show purchase product fields
  final bool showPurchaseProductFields;

  /// The default locale for the app
  final Locale defaultLocale;

  /// Whether to use static payment types instead of API-provided ones
  final bool showStaticPaymentTypes;

  /// Whether to show mentor vip level
  final bool showMentorVipLevel;

  /// The market color scheme to use (red up/green down or green up/red down)
  final MarketColor marketColor;

  /// Whether to disable upper case password
  final bool disableUpperCasePasswordProtection;

  /// Whether to show withdraw history
  final bool showWithdrawHistory;

  /// Whether to show app update
  final bool showAppUpdate;

  /// Whether to show static purchase percentage
  final bool showStaticPurchasePercentage;

  /// Whether to show add wallet address option in withdraw screen
  final bool showAddWalletAddress;

  /// Whether to show deposite tx hash
  final bool showDepositeTxHash;

  /// Singleton instance
  static AppConfig? _instance;

  /// Factory constructor that maintains the singleton pattern.
  ///
  /// Creates a new instance only if one doesn't exist, otherwise returns the existing instance.
  factory AppConfig({
    required Flavor flavor,
    required String appName,
    required String baseUrl,
    required String marketWsUrl,
    required String appUrl,
    required String icon,
    required String introVideo,
    required bool showDebugVersionTag,
    required bool fetchCommunityRecords,
    required bool showTradingWallet,
    required bool showTransferPreview,
    required bool showBenefitRules,
    required bool showTransfer,
    required bool showSmartInvestmentPurchasePercentage,
    required String accountType,
    required bool showPurchaseProductFields,
    required Locale defaultLocale,
    required bool showStaticPaymentTypes,
    required bool showMentorVipLevel,
    required MarketColor marketColor,
    required bool disableUpperCasePasswordProtection,
    required bool showWithdrawHistory,
    required bool showAppUpdate,
    required bool showStaticPurchasePercentage,
    required bool showAddWalletAddress,
    required bool showDepositeTxHash,
  }) {
    _instance ??= AppConfig._internal(
      flavor: flavor,
      appName: appName,
      baseUrl: baseUrl,
      marketWsUrl: marketWsUrl,
      appUrl: appUrl,
      icon: icon,
      introVideo: introVideo,
      showDebugVersionTag: showDebugVersionTag,
      fetchCommunityRecords: fetchCommunityRecords,
      showTradingWallet: showTradingWallet,
      showTransferPreview: showTransferPreview,
      showBenefitRules: showBenefitRules,
      showTransfer: showTransfer,
      showSmartInvestmentPurchasePercentage:
          showSmartInvestmentPurchasePercentage,
      accountType: accountType,
      showPurchaseProductFields: showPurchaseProductFields,
      defaultLocale: defaultLocale,
      showStaticPaymentTypes: showStaticPaymentTypes,
      showMentorVipLevel: showMentorVipLevel,
      marketColor: marketColor,
      disableUpperCasePasswordProtection: disableUpperCasePasswordProtection,
      showWithdrawHistory: showWithdrawHistory,
      showAppUpdate: showAppUpdate,
      showStaticPurchasePercentage: showStaticPurchasePercentage,
      showAddWalletAddress: showAddWalletAddress,
      showDepositeTxHash: showDepositeTxHash,
    );
    return _instance!;
  }

  /// Private constructor used by the factory constructor.
  AppConfig._internal({
    required this.flavor,
    required this.appName,
    required this.baseUrl,
    required this.marketWsUrl,
    required this.appUrl,
    required this.icon,
    required this.introVideo,
    required this.showDebugVersionTag,
    required this.fetchCommunityRecords,
    required this.showTradingWallet,
    required this.showTransferPreview,
    required this.showBenefitRules,
    required this.showTransfer,
    required this.showSmartInvestmentPurchasePercentage,
    required this.accountType,
    required this.showPurchaseProductFields,
    required this.defaultLocale,
    required this.showStaticPaymentTypes,
    required this.showMentorVipLevel,
    required this.marketColor,
    required this.disableUpperCasePasswordProtection,
    required this.showWithdrawHistory,
    required this.showAppUpdate,
    required this.showStaticPurchasePercentage,
    required this.showAddWalletAddress,
    required this.showDepositeTxHash,
  });

  /// Provides access to the singleton instance.
  ///
  /// Throws an assertion error if accessed before initialization.
  static AppConfig get instance {
    assert(_instance != null, 'AppConfig has not been initialized');
    return _instance!;
  }

  /// Gets the name of the current flavor
  String get flavorName => flavor.name;

  /// Gets the display title for the current flavor
  String get flavorTitle => appName;

  /// Gets the available payment types for the current flavor.
  ///
  /// If [apiWalletCoins] is provided and static payment types are disabled,
  /// returns payment types based on the API data.
  static List<PaymentTypeModel> getAvailablePaymentTypes({
    List<WalletCoinData>? apiWalletCoins,
  }) =>
      PaymentConfig.getAvailablePaymentTypes(
        apiWalletCoins: apiWalletCoins,
      );

  /// Gets the default payment type for the current flavor.
  ///
  /// Returns the first available payment type or null if none are available.
  static PaymentTypeModel? getDefaultPaymentType({
    List<WalletCoinData>? apiWalletCoins,
  }) =>
      PaymentConfig.getDefaultPaymentType(
        apiWalletCoins: apiWalletCoins,
      );

  /// Checks if wallet coins API should be called for the current flavor.
  ///
  /// Returns false if static payment types are enabled.
  static bool get shouldFetchWalletCoinsFromApi =>
      PaymentConfig.shouldFetchWalletCoinsFromApi;

  /// Gets PaymentType enum from payment code.
  ///
  /// Returns [PaymentType.ERC20] for ERC20 codes, [PaymentType.TRC20] otherwise.
  static PaymentType getPaymentTypeFromCode(String paymentCode) =>
      PaymentConfig.getPaymentTypeFromCode(paymentCode);

  /// Configures the locale for the current flavor.
  ///
  /// If [isoCode] is provided and not empty, finds the corresponding locale
  /// from supported languages and sets it. Otherwise, uses the default locale
  /// for the current flavor (Spanish for CFROEX flavor).
  ///
  /// Requires a mounted [BuildContext].
  static Future<void> configureLocale(BuildContext context,
      {String? isoCode}) async {
    final isUserSelectedLanguage = SharedPreferenceHelper()
        .readBoolData(SharedPreferencesKeys.userSelectedLanguage);
    if (isUserSelectedLanguage ?? false) {
      return;
    }

    Locale localeToSet;
    if (isoCode?.isNotEmpty ?? false) {
      try {
        final matchingLocaleModel = supportedLanguages.firstWhere(
          (model) => model.isoCode.toUpperCase() == isoCode!.toUpperCase(),
        );
        localeToSet = matchingLocaleModel.locale;
      } catch (_) {
        localeToSet = instance.defaultLocale;
      }
    } else {
      localeToSet = instance.defaultLocale;
    }

    await context.setLocale(localeToSet);

    if (context.mounted) {
      getIt<LanguageRepository>().setIntlDefaultLocale(context);
    }
  }

  // --- Purchase Percentage Configuration Methods ---

  /// Determines whether the app should fetch purchase percentages from the API.
  ///
  /// Returns true if:
  /// - Smart investment purchase percentage feature is enabled
  /// - Static purchase percentages are disabled
  static bool get shouldFetchPurchasePercentageFromApi {
    final config = AppConfig.instance;
    return config.showSmartInvestmentPurchasePercentage &&
        !config.showStaticPurchasePercentage;
  }

  /// Determines if purchase percentage options should be displayed in the UI.
  ///
  /// Returns true if:
  /// - Smart investment purchase percentage feature is enabled AND either:
  ///   - Static percentages are enabled OR
  ///   - Valid API configuration data is available
  ///
  /// @param apiConfigData Optional API configuration data containing available percentages
  static bool shouldShowSmartInvestmentPurchasePercentage({
    OrderRateData? apiConfigData,
  }) {
    final config = AppConfig.instance;
    return config.showSmartInvestmentPurchasePercentage &&
        (config.showStaticPurchasePercentage ||
            !PurchasePercentageConfig.isApiConfigEmpty(apiConfigData));
  }

  /// Gets the list of available purchase percentages.
  ///
  /// @param apiData Optional API data containing available percentages
  /// @param selectedPercentage Optional currently selected percentage
  /// @return List of PurchasePercentage objects representing available options
  static List<PurchasePercentage> getList({
    OrderRateData? apiData,
    String? selectedPercentage,
  }) {
    return PurchasePercentageConfig.getList(
        apiData: apiData, selected: selectedPercentage);
  }

  /// Checks if a specific purchase percentage is enabled for selection.
  ///
  /// @param percentage The percentage value to check
  /// @param apiConfigData Optional API configuration data
  /// @return true if the percentage is valid and enabled
  static bool isPurchasePercentageEnabled({
    required String percentage,
    OrderRateData? apiConfigData,
  }) {
    return PurchasePercentageConfig.isEnabled(percentage,
        apiData: apiConfigData);
  }

  /// Validates if a percentage selection is allowed based on current configuration.
  ///
  /// @param percentage The percentage value to validate
  /// @param apiData Optional API configuration data
  /// @return true if the percentage is valid
  static bool isValid(String percentage, {OrderRateData? apiData}) {
    return PurchasePercentageConfig.isEnabled(percentage, apiData: apiData);
  }

  /// Gets the default purchase percentage based on current configuration.
  ///
  /// @param apiData Optional API configuration data
  /// @return String representing the default percentage value
  static String getDefault({OrderRateData? apiData}) {
    return PurchasePercentageConfig.getDefault(apiData: apiData);
  }

  /// Gets the supported languages based on the current flavor
  ///
  /// SIS flavor supports all languages
  /// Other flavors only support the base languages
  static List<LocaleModel> get supportedLanguages {
    switch (F.appFlavor) {
      case Flavor.sis:
      case Flavor.cfroex:
        // SIS flavor supports all languages
        return AppConstants.supportedLanguages;
      case Flavor.sf_app:
      case Flavor.ncm:
        // Other flavors only support base languages
        return AppConstants.baseSupportedLanguages;
    }
  }

  /// Gets the static payment types for the current flavor.
  ///
  /// Returns the static payment types for the current flavor.
  static List<PaymentTypeModel> getStaticPaymentTypesInAddWithdrawAddress() =>
      PaymentConfig.getStaticPaymentTypesInAddWithdrawAddress();
}
