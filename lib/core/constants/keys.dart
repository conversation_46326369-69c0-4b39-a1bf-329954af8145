import 'package:easy_localization/easy_localization.dart';
import 'package:sf_app/core/constants/assets.dart';
import 'package:sf_app/core/constants/string_constants.dart';

/// Keys used for local storage
class LocalStorageKeys {
  static const String token = 'token'; // JWT auth token
  static const String refreshToken = 'refreshToken'; // Refresh token for auth
}

/// Keys used for shared preferences storage
class SharedPreferencesKeys {
  static const String isLoggedIn = 'isLoggedIn'; // User login status
  static const String isAccountStatusLogged = 'isAccountStatusLogged'; // Account status logging flag
  static const String user = 'user'; // User data
  static const String isOnBoard = 'isOnBoard'; // Onboarding completion status
  static const String isWalletPasswordSet = 'isWalletPasswordSet'; // Wallet password setup status
  static const String chatConfig = 'chatConfig'; // Chat configuration
  static const String userSelectedLanguage = 'userSelectedLanguage'; // User manually selected language ISO code
}

/// Mapping of cryptocurrency trading pairs to their asset icons
const coins = {
  'BTCUSDT': Assets.btc,
  'ETHUSDT': Assets.eth, 
  'BNBUSDT': Assets.bnb,
  'MATICUSDT': Assets.matic,
  'ATOMUSDT': Assets.atom,
  'AVAXUSDT': Assets.avax,
  'NEARUSDT': Assets.near,
};

/// Mapping of cryptocurrency trading pairs to their full names
const coinsText = {
  'BTCUSDT': 'Bitcoin',
  'ETHUSDT': 'Ethereum',
  'BNBUSDT': 'Binance Coin',
  'MATICUSDT': 'Polygon',
  'ATOMUSDT': 'Cosmos',
  'AVAXUSDT': 'Avalanche',
  'NEARUSDT': 'NEAR Protocol',
};

/// Mapping of contract type IDs to their localized names
var contractTypes = {
  '1': StringConstants.contractType1.tr(),
  '2': StringConstants.contractType2.tr(),
  '3': StringConstants.contractType3.tr(),
};
