import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:injectable/injectable.dart';
import 'package:sf_app/core/shared/logic/country_code/country_code_cubit.dart';
import 'package:sf_app/core/config/app_config.dart';

import '../../constants/keys.dart';
import '../../models/Locale/locale_model.dart';
import '../../utils/shared_preference_helper.dart';
import 'language_repository.dart';

/// Service class that handles language and localization functionality
///
/// Implements [LanguageRepository] interface to provide language handling capabilities
@LazySingleton(as: LanguageRepository)
class LanguageService implements LanguageRepository {
  /// Gets list of supported locales based on the current flavor
  ///
  /// For SIS flavor: Returns all languages including additional European languages
  /// For other flavors: Returns only the base languages
  @override
  List<LocaleModel> getSupportedLocales(BuildContext context) {
    final supportedLocales = AppConfig.supportedLanguages
      ..sort((a, b) => a.sort.compareTo(b.sort));
    //hide some locales
    final hiddenLocales = ['my', 'hk'];
    return supportedLocales.where((locale) {
      final countryCode = locale.locale.countryCode?.toLowerCase();
      return !hiddenLocales.contains(countryCode);
    }).toList();
  }

  /// Gets current locale from the context
  @override
  Locale getCurrentLocale(BuildContext context) {
    return context.locale;
  }

  /// Sets the app locale to the specified locale if supported
  ///
  /// Updates the Intl default locale after changing the app locale
  /// Only allows setting locales that are supported by the current flavor
  @override
  Future<void> setLocale(BuildContext context, Locale locale) async {
    final flavorSupportedLocales = AppConfig.supportedLanguages;
    if (flavorSupportedLocales.any((e) => e.locale == locale)) {
      await context.setLocale(locale);
      if (context.mounted) {
        setIntlDefaultLocale(context);
      }
      SharedPreferenceHelper()
          .writeBoolData(SharedPreferencesKeys.userSelectedLanguage, true);
    }
  }

  /// Gets flag emoji for the specified locale using country codes
  ///
  /// Returns US flag emoji as fallback if locale or country not found
  @override
  String getFlagEmoji(BuildContext context, Locale? locale) {
    final countries = context.read<CountryCodeCubit>().state.countries;
    if (locale == null || countries.isEmpty) return '🇺🇸';

    try {
      final country = countries.firstWhere(
        (country) =>
            country.code.toUpperCase() == locale.countryCode?.toUpperCase(),
        orElse: () => countries.first,
      );
      return country.flagEmoji;
    } catch (e) {
      return '🇺🇸'; // Default fallback
    }
  }

  /// Gets localized language name for the specified locale
  @override
  String getLanguageName(Locale locale) {
    return _getLanguageName(locale);
  }

  /// Checks if the specified locale is currently selected
  @override
  bool isLocaleSelected(BuildContext context, Locale locale) {
    return getCurrentLocale(context) == locale;
  }

  /// Sets the Intl default locale based on current app locale
  @override
  void setIntlDefaultLocale(BuildContext context) {
    final locale = getCurrentLocale(context);
    Intl.defaultLocale = '${locale.languageCode}_${locale.countryCode!}';
  }

  /// Helper method to get localized language name based on country code
  ///[use country code instead of language code]
  ///
  String _getLanguageName(Locale locale) =>
      switch (locale.countryCode?.toLowerCase()) {
        'us' => 'English',
        'kr' => '한국인',
        'br' => 'Português',
        'fr' => 'Français',
        'in' => 'हिंदी',
        'es' => 'Español',
        'jp' => '日本語',
        'cn' => '繁体中文',
        'sa' => 'العربية',
        'de' => 'Deutsch',
        'it' => 'Italiano',
        'cz' => 'Čeština',
        'nl' => 'Nederlands',
        'no' => 'Norsk',
        'pl' => 'Polski',
        'se' => 'Svenska',
        'ru' => 'Русский',
        // 'my' => 'Bahasa Melayu (Malaysia)',
        'ua' => 'Українська',
        'lt' => 'Lietuvių',
        'gr' => 'Ελληνικά',
        'dk' => 'Dansk',
        'fi' => 'Suomi',
        'hu' => 'Magyar',
        'ee' => 'Eesti',
        'lv' => 'Latviešu',
        'sk' => 'Slovenčina',
        'si' => 'Slovenščina',
        'tr' => 'Türkçe',
        'mx' => 'Español América',
        // 'hk' => '繁體中文 (香港)',
        _ => locale.languageCode.toUpperCase(),
      };
}
