import 'package:draggable_float_widget/draggable_float_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app/core/constants/assets.dart';

import '../routes/routes.dart';
import '../theme/my_color_scheme.dart';

class ChatFloatingButton extends StatelessWidget  {
  const ChatFloatingButton({
    super.key,
  });

  void _onChatButtonPressed(BuildContext context) {
    Navigator.pushNamed(context, routeCustomerSupportScreen);
  }

  Widget _buildFloatingButton(BuildContext context) {
    return FloatingActionButton(
      elevation: 1,
      shape: const CircleBorder(),
      backgroundColor: myColorScheme(context).primaryColor,
      onPressed: () => _onChatButtonPressed(context),
      child: SvgPicture.asset(
        Assets.chatIcon,
        width: 35.w,
        height: 35.h,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return _buildFloatingButton(context);
  }
}

class DraggableChatFloating<PERSON>utton extends StatelessWidget {
  const DraggableChatFloatingButton(
      {super.key, this.initPositionYMarginBorder});
  final double? initPositionYMarginBorder;

  @override
  Widget build(BuildContext context) {
    return DraggableFloatWidget(
      config: DraggableFloatWidgetBaseConfig(
        isFullScreen: false,
        initPositionYInTop: false,
        initPositionXInLeft: false,
        initPositionYMarginBorder: initPositionYMarginBorder ?? 0,
        borderTop: MediaQuery.of(context).padding.top,
        borderRight: 16.w,
        borderLeft: 16.w,
        borderBottom: 0,
      ),
      child: const ChatFloatingButton(),
    );
  }
}
