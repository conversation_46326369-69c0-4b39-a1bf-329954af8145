import 'package:carousel_slider/carousel_slider.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:sf_app/core/constants/enums.dart';
import 'package:sf_app/core/extention.dart';
import 'package:sf_app/core/routes/routes.dart';
import 'package:sf_app/core/shared/logic/app_data/app_data_cubit.dart';
import 'package:sf_app/core/theme/color_pallette.dart';
import 'package:sf_app/core/theme/font_pallette.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/core/utils/shared_preference_helper.dart';
import 'package:sf_app/features/community/domain/models/community_list.dart';
import 'package:sf_app/features/community/domain/models/agent_commission_stats/agent_commission_stats.dart';
import 'package:sf_app/features/community/logic/community/community_cubit.dart';
import 'package:sf_app/features/community/widgets/community_auth.dart';
import 'package:sf_app/features/community/widgets/community_header.dart';
import 'package:sf_app/features/community/widgets/community_shimmer.dart';
import 'package:sf_app/features/community/widgets/agent_commission_shimmer.dart';
import '../../../core/common_function.dart';
import '../../../core/constants/assets.dart';
import '../../../core/constants/string_constants.dart';
import '../../../core/utils/mixin/animation.dart';
import '../../../core/widgets/common_empty_data.dart';
import '../../../core/widgets/common_image_carousel.dart';
import '../../../core/widgets/common_text_field.dart';
import '../../home/<USER>/models/carousel/carousel_list.dart';
import '../../profile/domain/models/profile/profile_info.dart';
import '../../profile/logic/profile/profile_cubit.dart';
import '../widgets/community_header_tile.dart';

class CommunityScreen extends StatefulWidget {
  final bool enableBack;

  const CommunityScreen({super.key, this.enableBack = false});

  @override
  State<CommunityScreen> createState() => _CommunityScreenState();
}

class _CommunityScreenState extends State<CommunityScreen>
    with StaggeredAnimation {
  final TextEditingController _invitationLinkController =
      TextEditingController();
  final TextEditingController _invitationCodeController =
      TextEditingController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CommunityCubit>()
        ..getCommunityList()
        ..getCarouselList()
        ..getAgentCommissionStats();
    });
  }

  Future<bool> _checkSignedIn() async {
    return SharedPreferenceHelper().getIsLoggedIn() ?? false;
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: _checkSignedIn(),
      builder: (context, snapshot) {
        if (!(snapshot.data ?? true)) {
          return const CommunityScreenAuth();
        }

        return Scaffold(
          appBar: _buildAppBar(),
          body: _buildMainContent(),
        );
      },
    );
  }

  PreferredSize _buildAppBar() {
    return PreferredSize(
      preferredSize: const Size.fromHeight(50),
      child: AppBar(
        systemOverlayStyle: SystemUiOverlayStyle.light,
        elevation: 0,
        backgroundColor: myColorScheme(context).primaryColor,
        leading: widget.enableBack
            ? IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.pop(context),
              )
            : null,
        title: Text(
          StringConstants.community.tr(),
          style: FontPalette.semiBold20.copyWith(color: Colors.white),
        ),
      ),
    );
  }

  Widget _buildMainContent() {
    return RefreshIndicator(
      onRefresh: () async {
        context.read<CommunityCubit>()
          ..getCommunityList()
          ..getCarouselList()
          ..getAgentCommissionStats();
      },
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: AnimationLimiter(
          child: Stack(
            children: [
              const CommunityHeaderBackground(height: 80),
              Padding(
                padding: EdgeInsets.all(12.r),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: staggeredAnimation(
                    children: [
                      20.verticalSpace,
                      _buildProfileHeader(),
                      20.verticalSpace,
                      _buildLinkSection(),
                      20.verticalSpace,
                      _buildAgentCommissionStatsSection(),
                      20.verticalSpace,
                      _buildCarouselSection(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfileHeader() {
    return BlocSelector<ProfileCubit, ProfileState, (DataStatus, InfoData?)>(
      selector: (state) => (state.infoFetchStatus, state.infoData),
      builder: (context, data) {
        if (data.$1 == DataStatus.loading) {
          return const ProfileHeaderShimmer();
        }

        final email = context.read<AppDataCubit>().state.userData?.email;
        final username = context.read<AppDataCubit>().state.userData?.username;
        return CommunityHeaderTile(
          id: data.$2?.userId.toString() ?? '',
          email: (email?.isNotEmpty == true ? email : username) ??
              StringConstants.user.tr(),
          vipLevel: data.$2?.userLevel.toString() ?? '',
          onTap: () {},
        );
      },
    );
  }

  BlocSelector<CommunityCubit, CommunityState, (DataStatus, CommunityList?)>
      communityItem() {
    return BlocSelector<CommunityCubit, CommunityState,
        (DataStatus, CommunityList?)>(
      selector: (state) => (state.communityFetchStatus, state.communityList),
      builder: (_, data) {
        switch (data.$1) {
          case DataStatus.success:
            if (data.$2?.data.list.isEmpty ?? true) {
              return CommonEmpty(topPadding: .3.sh);
            }
            return ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              itemCount: data.$2?.data.list.length ?? 0,
              shrinkWrap: true,
              itemBuilder: (context, index) {
                final item = data.$2?.data.list[index];
                return CommunityItem(
                  title: item?.username ?? StringConstants.user.tr(),
                  iconPath: CommonFunctions.getIconFromLevel(item?.level ?? 0),
                );
              },
            );
          case DataStatus.loading:
            return const CommunityShimmer();
          default:
            return SizedBox(width: 0.2.sw, height: 45.h);
        }
      },
    );
  }

  Widget _buildLinkSection() {
    return BlocSelector<ProfileCubit, ProfileState,
        (DataStatus, InfoData?, bool, String?)>(
      selector: (state) => (
        state.infoFetchStatus,
        state.infoData,
        state.onCopiedInvitation,
        state.firebaseShortUrl,
      ),
      builder: (_, data) => _buildLinkContent(data),
    );
  }

  Widget _buildLinkContent((DataStatus, InfoData?, bool, String?) data) {
    if (data.$1 == DataStatus.loading) {
      return const InvitationLinksShimmer();
    }
    if (data.$1 != DataStatus.success || data.$2 == null) {
      return _buildEmptyState();
    }
    return Column(
      children: [
        _buildInvitationLink(data),
        20.verticalSpace,
        _buildInvitationCode(),
      ],
    );
  }

  Widget _buildInvitationLink((DataStatus, InfoData?, bool, String?) data) {
    return CommonTextField(
      isEditable: false,
      showCustomSuffixBox: true,
      customSuffixText:
          data.$3 ? StringConstants.copied.tr() : StringConstants.copy.tr(),
      onSuffixIconTap: () =>
          context.read<ProfileCubit>().manageOnCopiedInvitation(),
      customSuffixBackgroundColor: myColorScheme(context).iconBackgroundColor2,
      textInputType: TextInputType.url,
      textInputAction: TextInputAction.next,
      labelText: StringConstants.invitationLinkLabelText.tr(),
      hintStyle: FontPalette.medium12
          .copyWith(color: myColorScheme(context).titleColor),
      hintText: data.$4,
      controller: _invitationLinkController,
      onChanged: (_) {},
    );
  }

  Widget _buildInvitationCode() {
    return BlocSelector<ProfileCubit, ProfileState,
        (DataStatus, InfoData?, bool)>(
      selector: (state) => (
        state.infoFetchStatus,
        state.infoData,
        state.onCopiedCode,
      ),
      builder: (context, state) {
        return CommonTextField(
          isEditable: false,
          showCustomSuffixBox: true,
          customSuffixText: state.$3
              ? StringConstants.copied.tr()
              : StringConstants.copy.tr(),
          onSuffixIconTap: () =>
              context.read<ProfileCubit>().manageOnCopiedInvitationCode(),
          customSuffixBackgroundColor:
              myColorScheme(context).iconBackgroundColor2,
          textInputType: TextInputType.url,
          textInputAction: TextInputAction.next,
          labelText: StringConstants.invitationCode.tr(),
          hintStyle: FontPalette.medium12
              .copyWith(color: myColorScheme(context).titleColor),
          hintText: state.$2?.invitationCode,
          controller: _invitationCodeController,
          onChanged: (_) {},
        );
      },
    );
  }

  Widget _buildEmptyState() => SizedBox(width: 0.2.sw, height: 45.h);

  Widget buildCommunityListSection() {
    return Container(
      padding: EdgeInsets.all(12.r),
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(color: const Color(0xFFE5EAF2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            StringConstants.community.tr(),
            style: FontPalette.semiBold16.copyWith(
              color: ColorPalette.primaryColor,
            ),
          ),
          16.verticalSpace,
          // communityItem(),
        ],
      ),
    );
  }

  Widget _buildCarouselSection() {
    return BlocBuilder<CommunityCubit, CommunityState>(
      builder: (context, state) {
        return Container(
          margin: EdgeInsets.only(bottom: 20.h),
          width: double.infinity,
          child: CommonImageCarousel<CarouselData>(
            items: state.carouselList,
            loadingStatus: state.carouselFetchStatus,
            imageUrlExtractor: (item) => item.imgUrl,
            onItemTap: (item) {
              if (item.linkUrl.isNotEmpty) {
                CommonFunctions.launchUrls(item.linkUrl);
              }
            },
            itemHeight: 200.h,
            shimmerHeight: 200.h,
            carouselOptions: CarouselOptions(
              viewportFraction: 1,
              enableInfiniteScroll: false,
              autoPlay: false,
              autoPlayInterval: const Duration(seconds: 3),
              autoPlayAnimationDuration: const Duration(milliseconds: 800),
              autoPlayCurve: Curves.fastOutSlowIn,
              enlargeCenterPage: true,
            ),
            activeIndicatorColor: Theme.of(context).primaryColor,
            inactiveIndicatorColor: Colors.grey.shade300,
          ),
        );
      },
    );
  }

  Widget _buildAgentCommissionStatsSection() {
    return BlocSelector<CommunityCubit, CommunityState,
        (DataStatus, AgentCommissionStatsData?)>(
      selector: (state) =>
          (state.agentCommissionStatsFetchStatus, state.agentCommissionStats),
      builder: (context, data) {
        return Container(
          padding: EdgeInsets.all(12.r),
          decoration: BoxDecoration(
            color: myColorScheme(context).cardColor,
            borderRadius: BorderRadius.circular(16.r),
            border: Border.all(
                color: myColorScheme(context).borderColor ??
                    const Color(0xFFE5EAF2)),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // if (data.$1 == DataStatus.loading)
              if (data.$1 == DataStatus.loading)
                const AgentCommissionShimmer()
              else if (data.$1 == DataStatus.success && data.$2 != null)
                _buildAgentCommissionStatsContent(data.$2!)
              else
                _buildAgentCommissionStatsError(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildAgentCommissionStatsContent(AgentCommissionStatsData data) {
    return Column(
      children: [
        // Stats Grid (2x2)
        GridView.count(
          crossAxisCount: 2,
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          mainAxisSpacing: 10.r,
          crossAxisSpacing: 10.r,
          childAspectRatio: 179 / 69,
          children: [
            _statCard(
              icon: Assets.member,
              label: StringConstants.directMembers.tr(),
              value: '${data.directMembers ?? 0}',
            ),
            _statCard(
              icon: Assets.members,
              label: StringConstants.totalMembers.tr(),
              value: '${data.totalMembers ?? 0}',
            ),
            _statCard(
              icon: Assets.other,
              label: StringConstants.otherMembers.tr(),
              value: '${data.otherMembers ?? 0}',
            ),
            _statCard(
              icon: '',
              label: StringConstants.totalRevenue.tr(),
              value: (data.totalRevenue ?? 0).toStringAsFixed(2).toCurrency(),
              isCurrency: true,
            ),
          ],
        ),
        24.verticalSpace,
        // Generation Sections
        _generationCard(StringConstants.firstGeneration.tr(),
            members: data.firstGeneration?.memberCount ?? 0,
            revenue: (data.firstGeneration?.revenue ?? 0)
                .toStringAsFixed(2)
                .toCurrency(),
            onTap: () => Navigator.pushNamed(context, routeMemberInfoScreen,
                arguments: {'level': 1})),
        12.verticalSpace,
        _generationCard(StringConstants.secondGeneration.tr(),
            members: data.secondGeneration?.memberCount ?? 0,
            revenue: (data.secondGeneration?.revenue ?? 0)
                .toStringAsFixed(2)
                .toCurrency(),
            onTap: () => Navigator.pushNamed(context, routeMemberInfoScreen,
                arguments: {'level': 2})),
        12.verticalSpace,
        _generationCard(StringConstants.thirdGeneration.tr(),
            members: data.thirdGeneration?.memberCount ?? 0,
            revenue: (data.thirdGeneration?.revenue ?? 0)
                .toStringAsFixed(2)
                .toCurrency(),
            onTap: () => Navigator.pushNamed(context, routeMemberInfoScreen,
                arguments: {'level': 3})),
      ],
    );
  }

  Widget _buildAgentCommissionStatsError() {
    return SizedBox(
      height: 200.h,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48.r,
              color: Colors.grey,
            ),
            16.verticalSpace,
            Text(
              StringConstants.failedToLoadCommissionStats.tr(),
              style: FontPalette.medium14.copyWith(color: Colors.grey),
            ),
            8.verticalSpace,
            TextButton(
              onPressed: () =>
                  context.read<CommunityCubit>().getAgentCommissionStats(),
              child: Text(StringConstants.retry.tr()),
            ),
          ],
        ),
      ),
    );
  }

  Widget _statCard({
    required String icon,
    required String label,
    required String value,
    bool isCurrency = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: myColorScheme(context).cardColor,
        borderRadius: BorderRadius.circular(16.r),
        border: Border.all(
            color:
                myColorScheme(context).borderColor ?? const Color(0xFFE5EAF2)),
      ),
      padding: EdgeInsets.all(12.r),
      child: Row(
        children: [
          Container(
              width: 28.r,
              height: 28.r,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: Theme.of(context).brightness == Brightness.dark
                      ? [
                          const Color(0xFF607CA3),
                          const Color(0xFFABBBDD),
                        ]
                      : [
                          const Color(0xFFF0F7FF),
                          const Color(0xFFE6F4FF),
                        ],
                ),
                borderRadius: BorderRadius.circular(7.r),
              ),
              child: isCurrency
                  ? Center(
                      child: Text(
                        '\$',
                        style: FontPalette.normal17.copyWith(
                          color: myColorScheme(context).primaryColor,
                        ),
                      ),
                    )
                  : Padding(
                      padding: const EdgeInsets.all(5.0),
                      child: SvgPicture.asset(
                        icon,
                        colorFilter: ColorFilter.mode(
                            ColorPalette.primaryColor, BlendMode.srcIn),
                      ),
                    )),
          8.horizontalSpace,
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: FontPalette.semiBold12.copyWith(
                  color: myColorScheme(context).primaryColor,
                ),
              ),
              Text(
                value,
                style: FontPalette.semiBold16.copyWith(
                  color: myColorScheme(context).primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _generationCard(String title,
      {required int members,
      required String revenue,
      required VoidCallback onTap}) {
    return Bounceable(
      onTap: onTap,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            height: 70.h,
            decoration: BoxDecoration(
              color: myColorScheme(context).cardColor,
              borderRadius: BorderRadius.circular(8.r),
              border: Border.all(
                  color: myColorScheme(context).borderColor ??
                      const Color(0xFFE5EAF2)),
              boxShadow: const [
                BoxShadow(
                  offset: Offset(0, 4),
                  blurRadius: 12,
                  color: Color(0x141890FF),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(left: 16.r, top: 8.r),
                  child: Row(
                    children: [
                      Text(
                        title,
                        style: FontPalette.semiBold13.copyWith(
                          color: myColorScheme(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
                2.verticalSpace,
                Container(
                  height: 30.h,
                  margin: EdgeInsets.symmetric(
                    horizontal: 12.r,
                  ),
                  padding:
                      EdgeInsets.symmetric(vertical: 4.r, horizontal: 16.r),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: Theme.of(context).brightness == Brightness.dark
                          ? [
                              const Color(0xFF607CA3),
                              const Color(0xFFABBBDD),
                            ]
                          : [
                              const Color(0xFFF0F7FF),
                              const Color(0xFFE6F4FF),
                            ],
                    ),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${StringConstants.members.tr()}: $members',
                        style: FontPalette.semiBold13.copyWith(
                          color: ColorPalette.primaryColor,
                        ),
                      ),
                      Text(
                        '${StringConstants.revenue.tr()}: $revenue',
                        style: FontPalette.semiBold13.copyWith(
                          color: myColorScheme(context).primaryColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
              top: 35.h,
              left: -5.w,
              child: Container(
                width: 11.r, // Outer circle size
                height: 11.r,
                decoration: BoxDecoration(
                  color: myColorScheme(context).primaryColor ??
                      ColorPalette.primaryColor
                          .withAlpha(100), // Outer ring color
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Container(
                    width: 8.r, // Inner circle size
                    height: 8.r,
                    decoration: BoxDecoration(
                      color: myColorScheme(context).primaryColor ??
                          ColorPalette.primaryColor, // Main dot color
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
              )),
        ],
      ),
    );
  }
}
