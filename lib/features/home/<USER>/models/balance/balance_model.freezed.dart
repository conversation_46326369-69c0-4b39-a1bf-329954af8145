// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'balance_model.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

BalanceModel _$BalanceModelFromJson(Map<String, dynamic> json) {
  return _BalanceModel.fromJson(json);
}

/// @nodoc
mixin _$BalanceModel {
  int get code => throw _privateConstructorUsedError;
  BalanceData get data => throw _privateConstructorUsedError;
  String get msg => throw _privateConstructorUsedError;

  /// Serializes this BalanceModel to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BalanceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BalanceModelCopyWith<BalanceModel> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BalanceModelCopyWith<$Res> {
  factory $BalanceModelCopyWith(
          BalanceModel value, $Res Function(BalanceModel) then) =
      _$BalanceModelCopyWithImpl<$Res, BalanceModel>;
  @useResult
  $Res call({int code, BalanceData data, String msg});

  $BalanceDataCopyWith<$Res> get data;
}

/// @nodoc
class _$BalanceModelCopyWithImpl<$Res, $Val extends BalanceModel>
    implements $BalanceModelCopyWith<$Res> {
  _$BalanceModelCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BalanceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? data = null,
    Object? msg = null,
  }) {
    return _then(_value.copyWith(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as BalanceData,
      msg: null == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String,
    ) as $Val);
  }

  /// Create a copy of BalanceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $BalanceDataCopyWith<$Res> get data {
    return $BalanceDataCopyWith<$Res>(_value.data, (value) {
      return _then(_value.copyWith(data: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BalanceModelImplCopyWith<$Res>
    implements $BalanceModelCopyWith<$Res> {
  factory _$$BalanceModelImplCopyWith(
          _$BalanceModelImpl value, $Res Function(_$BalanceModelImpl) then) =
      __$$BalanceModelImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({int code, BalanceData data, String msg});

  @override
  $BalanceDataCopyWith<$Res> get data;
}

/// @nodoc
class __$$BalanceModelImplCopyWithImpl<$Res>
    extends _$BalanceModelCopyWithImpl<$Res, _$BalanceModelImpl>
    implements _$$BalanceModelImplCopyWith<$Res> {
  __$$BalanceModelImplCopyWithImpl(
      _$BalanceModelImpl _value, $Res Function(_$BalanceModelImpl) _then)
      : super(_value, _then);

  /// Create a copy of BalanceModel
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? code = null,
    Object? data = null,
    Object? msg = null,
  }) {
    return _then(_$BalanceModelImpl(
      code: null == code
          ? _value.code
          : code // ignore: cast_nullable_to_non_nullable
              as int,
      data: null == data
          ? _value.data
          : data // ignore: cast_nullable_to_non_nullable
              as BalanceData,
      msg: null == msg
          ? _value.msg
          : msg // ignore: cast_nullable_to_non_nullable
              as String,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BalanceModelImpl implements _BalanceModel {
  const _$BalanceModelImpl(
      {required this.code, required this.data, required this.msg});

  factory _$BalanceModelImpl.fromJson(Map<String, dynamic> json) =>
      _$$BalanceModelImplFromJson(json);

  @override
  final int code;
  @override
  final BalanceData data;
  @override
  final String msg;

  @override
  String toString() {
    return 'BalanceModel(code: $code, data: $data, msg: $msg)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BalanceModelImpl &&
            (identical(other.code, code) || other.code == code) &&
            (identical(other.data, data) || other.data == data) &&
            (identical(other.msg, msg) || other.msg == msg));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, code, data, msg);

  /// Create a copy of BalanceModel
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BalanceModelImplCopyWith<_$BalanceModelImpl> get copyWith =>
      __$$BalanceModelImplCopyWithImpl<_$BalanceModelImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BalanceModelImplToJson(
      this,
    );
  }
}

abstract class _BalanceModel implements BalanceModel {
  const factory _BalanceModel(
      {required final int code,
      required final BalanceData data,
      required final String msg}) = _$BalanceModelImpl;

  factory _BalanceModel.fromJson(Map<String, dynamic> json) =
      _$BalanceModelImpl.fromJson;

  @override
  int get code;
  @override
  BalanceData get data;
  @override
  String get msg;

  /// Create a copy of BalanceModel
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BalanceModelImplCopyWith<_$BalanceModelImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

BalanceData _$BalanceDataFromJson(Map<String, dynamic> json) {
  return _BalanceData.fromJson(json);
}

/// @nodoc
mixin _$BalanceData {
  @JsonKey(name: 'cash')
  String? get cash => throw _privateConstructorUsedError;
  CommunityBalance? get community => throw _privateConstructorUsedError;

  /// Serializes this BalanceData to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of BalanceData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $BalanceDataCopyWith<BalanceData> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $BalanceDataCopyWith<$Res> {
  factory $BalanceDataCopyWith(
          BalanceData value, $Res Function(BalanceData) then) =
      _$BalanceDataCopyWithImpl<$Res, BalanceData>;
  @useResult
  $Res call({@JsonKey(name: 'cash') String? cash, CommunityBalance? community});

  $CommunityBalanceCopyWith<$Res>? get community;
}

/// @nodoc
class _$BalanceDataCopyWithImpl<$Res, $Val extends BalanceData>
    implements $BalanceDataCopyWith<$Res> {
  _$BalanceDataCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of BalanceData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cash = freezed,
    Object? community = freezed,
  }) {
    return _then(_value.copyWith(
      cash: freezed == cash
          ? _value.cash
          : cash // ignore: cast_nullable_to_non_nullable
              as String?,
      community: freezed == community
          ? _value.community
          : community // ignore: cast_nullable_to_non_nullable
              as CommunityBalance?,
    ) as $Val);
  }

  /// Create a copy of BalanceData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @pragma('vm:prefer-inline')
  $CommunityBalanceCopyWith<$Res>? get community {
    if (_value.community == null) {
      return null;
    }

    return $CommunityBalanceCopyWith<$Res>(_value.community!, (value) {
      return _then(_value.copyWith(community: value) as $Val);
    });
  }
}

/// @nodoc
abstract class _$$BalanceDataImplCopyWith<$Res>
    implements $BalanceDataCopyWith<$Res> {
  factory _$$BalanceDataImplCopyWith(
          _$BalanceDataImpl value, $Res Function(_$BalanceDataImpl) then) =
      __$$BalanceDataImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({@JsonKey(name: 'cash') String? cash, CommunityBalance? community});

  @override
  $CommunityBalanceCopyWith<$Res>? get community;
}

/// @nodoc
class __$$BalanceDataImplCopyWithImpl<$Res>
    extends _$BalanceDataCopyWithImpl<$Res, _$BalanceDataImpl>
    implements _$$BalanceDataImplCopyWith<$Res> {
  __$$BalanceDataImplCopyWithImpl(
      _$BalanceDataImpl _value, $Res Function(_$BalanceDataImpl) _then)
      : super(_value, _then);

  /// Create a copy of BalanceData
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? cash = freezed,
    Object? community = freezed,
  }) {
    return _then(_$BalanceDataImpl(
      cash: freezed == cash
          ? _value.cash
          : cash // ignore: cast_nullable_to_non_nullable
              as String?,
      community: freezed == community
          ? _value.community
          : community // ignore: cast_nullable_to_non_nullable
              as CommunityBalance?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$BalanceDataImpl implements _BalanceData {
  const _$BalanceDataImpl({@JsonKey(name: 'cash') this.cash, this.community});

  factory _$BalanceDataImpl.fromJson(Map<String, dynamic> json) =>
      _$$BalanceDataImplFromJson(json);

  @override
  @JsonKey(name: 'cash')
  final String? cash;
  @override
  final CommunityBalance? community;

  @override
  String toString() {
    return 'BalanceData(cash: $cash, community: $community)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$BalanceDataImpl &&
            (identical(other.cash, cash) || other.cash == cash) &&
            (identical(other.community, community) ||
                other.community == community));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode => Object.hash(runtimeType, cash, community);

  /// Create a copy of BalanceData
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$BalanceDataImplCopyWith<_$BalanceDataImpl> get copyWith =>
      __$$BalanceDataImplCopyWithImpl<_$BalanceDataImpl>(this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$BalanceDataImplToJson(
      this,
    );
  }
}

abstract class _BalanceData implements BalanceData {
  const factory _BalanceData(
      {@JsonKey(name: 'cash') final String? cash,
      final CommunityBalance? community}) = _$BalanceDataImpl;

  factory _BalanceData.fromJson(Map<String, dynamic> json) =
      _$BalanceDataImpl.fromJson;

  @override
  @JsonKey(name: 'cash')
  String? get cash;
  @override
  CommunityBalance? get community;

  /// Create a copy of BalanceData
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$BalanceDataImplCopyWith<_$BalanceDataImpl> get copyWith =>
      throw _privateConstructorUsedError;
}

CommunityBalance _$CommunityBalanceFromJson(Map<String, dynamic> json) {
  return _CommunityBalance.fromJson(json);
}

/// @nodoc
mixin _$CommunityBalance {
  @JsonKey(name: 'availableBalance')
  double? get availableBalance => throw _privateConstructorUsedError;
  @JsonKey(name: 'lockedBalance')
  double? get lockedBalance => throw _privateConstructorUsedError;
  @JsonKey(name: 'totalAmount')
  double? get totalAmount => throw _privateConstructorUsedError;

  /// Serializes this CommunityBalance to a JSON map.
  Map<String, dynamic> toJson() => throw _privateConstructorUsedError;

  /// Create a copy of CommunityBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  $CommunityBalanceCopyWith<CommunityBalance> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CommunityBalanceCopyWith<$Res> {
  factory $CommunityBalanceCopyWith(
          CommunityBalance value, $Res Function(CommunityBalance) then) =
      _$CommunityBalanceCopyWithImpl<$Res, CommunityBalance>;
  @useResult
  $Res call(
      {@JsonKey(name: 'availableBalance') double? availableBalance,
      @JsonKey(name: 'lockedBalance') double? lockedBalance,
      @JsonKey(name: 'totalAmount') double? totalAmount});
}

/// @nodoc
class _$CommunityBalanceCopyWithImpl<$Res, $Val extends CommunityBalance>
    implements $CommunityBalanceCopyWith<$Res> {
  _$CommunityBalanceCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  /// Create a copy of CommunityBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? availableBalance = freezed,
    Object? lockedBalance = freezed,
    Object? totalAmount = freezed,
  }) {
    return _then(_value.copyWith(
      availableBalance: freezed == availableBalance
          ? _value.availableBalance
          : availableBalance // ignore: cast_nullable_to_non_nullable
              as double?,
      lockedBalance: freezed == lockedBalance
          ? _value.lockedBalance
          : lockedBalance // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CommunityBalanceImplCopyWith<$Res>
    implements $CommunityBalanceCopyWith<$Res> {
  factory _$$CommunityBalanceImplCopyWith(_$CommunityBalanceImpl value,
          $Res Function(_$CommunityBalanceImpl) then) =
      __$$CommunityBalanceImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {@JsonKey(name: 'availableBalance') double? availableBalance,
      @JsonKey(name: 'lockedBalance') double? lockedBalance,
      @JsonKey(name: 'totalAmount') double? totalAmount});
}

/// @nodoc
class __$$CommunityBalanceImplCopyWithImpl<$Res>
    extends _$CommunityBalanceCopyWithImpl<$Res, _$CommunityBalanceImpl>
    implements _$$CommunityBalanceImplCopyWith<$Res> {
  __$$CommunityBalanceImplCopyWithImpl(_$CommunityBalanceImpl _value,
      $Res Function(_$CommunityBalanceImpl) _then)
      : super(_value, _then);

  /// Create a copy of CommunityBalance
  /// with the given fields replaced by the non-null parameter values.
  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? availableBalance = freezed,
    Object? lockedBalance = freezed,
    Object? totalAmount = freezed,
  }) {
    return _then(_$CommunityBalanceImpl(
      availableBalance: freezed == availableBalance
          ? _value.availableBalance
          : availableBalance // ignore: cast_nullable_to_non_nullable
              as double?,
      lockedBalance: freezed == lockedBalance
          ? _value.lockedBalance
          : lockedBalance // ignore: cast_nullable_to_non_nullable
              as double?,
      totalAmount: freezed == totalAmount
          ? _value.totalAmount
          : totalAmount // ignore: cast_nullable_to_non_nullable
              as double?,
    ));
  }
}

/// @nodoc
@JsonSerializable()
class _$CommunityBalanceImpl implements _CommunityBalance {
  const _$CommunityBalanceImpl(
      {@JsonKey(name: 'availableBalance') this.availableBalance,
      @JsonKey(name: 'lockedBalance') this.lockedBalance,
      @JsonKey(name: 'totalAmount') this.totalAmount});

  factory _$CommunityBalanceImpl.fromJson(Map<String, dynamic> json) =>
      _$$CommunityBalanceImplFromJson(json);

  @override
  @JsonKey(name: 'availableBalance')
  final double? availableBalance;
  @override
  @JsonKey(name: 'lockedBalance')
  final double? lockedBalance;
  @override
  @JsonKey(name: 'totalAmount')
  final double? totalAmount;

  @override
  String toString() {
    return 'CommunityBalance(availableBalance: $availableBalance, lockedBalance: $lockedBalance, totalAmount: $totalAmount)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CommunityBalanceImpl &&
            (identical(other.availableBalance, availableBalance) ||
                other.availableBalance == availableBalance) &&
            (identical(other.lockedBalance, lockedBalance) ||
                other.lockedBalance == lockedBalance) &&
            (identical(other.totalAmount, totalAmount) ||
                other.totalAmount == totalAmount));
  }

  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  int get hashCode =>
      Object.hash(runtimeType, availableBalance, lockedBalance, totalAmount);

  /// Create a copy of CommunityBalance
  /// with the given fields replaced by the non-null parameter values.
  @JsonKey(includeFromJson: false, includeToJson: false)
  @override
  @pragma('vm:prefer-inline')
  _$$CommunityBalanceImplCopyWith<_$CommunityBalanceImpl> get copyWith =>
      __$$CommunityBalanceImplCopyWithImpl<_$CommunityBalanceImpl>(
          this, _$identity);

  @override
  Map<String, dynamic> toJson() {
    return _$$CommunityBalanceImplToJson(
      this,
    );
  }
}

abstract class _CommunityBalance implements CommunityBalance {
  const factory _CommunityBalance(
          {@JsonKey(name: 'availableBalance') final double? availableBalance,
          @JsonKey(name: 'lockedBalance') final double? lockedBalance,
          @JsonKey(name: 'totalAmount') final double? totalAmount}) =
      _$CommunityBalanceImpl;

  factory _CommunityBalance.fromJson(Map<String, dynamic> json) =
      _$CommunityBalanceImpl.fromJson;

  @override
  @JsonKey(name: 'availableBalance')
  double? get availableBalance;
  @override
  @JsonKey(name: 'lockedBalance')
  double? get lockedBalance;
  @override
  @JsonKey(name: 'totalAmount')
  double? get totalAmount;

  /// Create a copy of CommunityBalance
  /// with the given fields replaced by the non-null parameter values.
  @override
  @JsonKey(includeFromJson: false, includeToJson: false)
  _$$CommunityBalanceImplCopyWith<_$CommunityBalanceImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
