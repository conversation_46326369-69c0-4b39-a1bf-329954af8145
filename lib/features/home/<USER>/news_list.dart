import 'package:flutter/material.dart';
import 'package:flutter_avif/flutter_avif.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_bounceable/flutter_bounceable.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:sf_app/core/routes/routes.dart';
import 'package:sf_app/core/theme/my_color_scheme.dart';
import 'package:sf_app/core/utils/convert_helper.dart';
import 'package:sf_app/core/widgets/common_shimmer.dart';

import 'package:sf_app/core/constants/string_constants.dart';
import 'package:sf_app/core/theme/color_pallette.dart';
import 'package:sf_app/core/theme/font_pallette.dart';
import 'package:sf_app/features/home/<USER>/home/<USER>';
import 'package:shimmer_animation/shimmer_animation.dart';

import '../../../core/constants/enums.dart';
import 'package:easy_localization/easy_localization.dart';

class NewsList extends StatelessWidget {
  const NewsList({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Text(
            StringConstants.newsUpdates.tr(),
            style: FontPalette.bold16
                .copyWith(color: myColorScheme(context).titleColor),
          ),
        ),
        26.verticalSpace,
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 20.w),
          child: BlocBuilder<HomeCubit, HomeState>(
            builder: (context, state) {
              return ListView.separated(
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.newsDataList?.data.newsDataList.length ?? 0,
                shrinkWrap: true,
                separatorBuilder: (_, __) => Divider(
                  height: 24.h,
                  thickness: 1,
                  color: ColorPalette.primaryColor.withValues(alpha: 0.05),
                ),
                itemBuilder: (context, index) {
                  final news = state.newsDataList?.data.newsDataList[index];
                  final isAvif = news?.coverUrl?.contains(".avif") ?? false;
                  if (state.newsUpdatesFetchStatus == DataStatus.loading &&
                      state.newsDataList?.data.newsDataList.isEmpty == true) {
                    return const NewsListItemShimmer();
                  }
                  return Bounceable(
                    onTap: () => Navigator.pushNamed(
                      context,
                      routeNewsDetailScreen,
                      arguments: {"id": news?.id ?? 0},
                    ),
                    child: Container(
                      color: Colors.transparent,
                      child: Padding(
                        padding: EdgeInsets.symmetric(vertical: 8.h),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Hero(
                              tag: news?.id ?? 0,
                              child: _NewsImage(
                                imageUrl: news?.coverUrl ?? '',
                                isAvif: isAvif,
                              ),
                            ),
                            16.horizontalSpace,
                            _NewsContent(
                              title: news?.title ?? '',
                              datetime: ConvertHelper.formatDateMonthDay(
                                news?.createTime.toString() ?? '',
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              );
            },
          ),
        ),
      ],
    );
  }
}

class _NewsImageShimmer extends StatelessWidget {
  const _NewsImageShimmer();

  @override
  Widget build(BuildContext context) {
    return CommonShimmer(
      width: 120.w,
      height: 80.h,
    );
  }
}

class _ErrorImage extends StatelessWidget {
  const _ErrorImage();

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: ColorPalette.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.image_not_supported_rounded,
            color: ColorPalette.primaryColor.withValues(alpha: 0.3),
            size: 24.w,
          ),
          SizedBox(height: 4.h),
          Text(
            'Image not available',
            style: FontPalette.semiBold9.copyWith(
              color: ColorPalette.primaryColor.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }
}

class _NewsImage extends StatelessWidget {
  final String imageUrl;
  final bool isAvif;

  const _NewsImage({
    required this.imageUrl,
    required this.isAvif,
  });

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8.r),
      child: SizedBox(
        width: 120.w,
        height: 80.h,
        child: isAvif
            ? AvifImage.network(
                imageUrl,
                fit: BoxFit.cover,
                loadingBuilder: (_, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const _NewsImageShimmer();
                },
                errorBuilder: (_, __, ___) => const _ErrorImage(),
              )
            : Image.network(
                imageUrl,
                fit: BoxFit.cover,
                loadingBuilder: (_, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return const _NewsImageShimmer();
                },
                errorBuilder: (_, __, ___) => const _ErrorImage(),
              ),
      ),
    );
  }
}

class _NewsContent extends StatelessWidget {
  final String title;
  final String datetime;

  const _NewsContent({
    required this.title,
    required this.datetime,
  });

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: FontPalette.semiBold14.copyWith(
              color: myColorScheme(context).primaryColor,
              height: 1.2,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
          8.verticalSpace,
          Text(
            datetime,
            style: FontPalette.semiBold9.copyWith(
              color: myColorScheme(context).labelColor,
            ),
          ),
        ],
      ),
    );
  }
}

class NewsListItemShimmer extends StatelessWidget {
  const NewsListItemShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Shimmer(
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 8.h),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Shimmer for Image
            Container(
              width: 120.w,
              height: 80.h,
              decoration: BoxDecoration(
                color: myColorScheme(context).cardColor,
                borderRadius: BorderRadius.circular(8.r),
              ),
            ),
            16.horizontalSpace,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Shimmer for Title
                  Container(
                    width: double.infinity,
                    height: 16.h,
                    color: myColorScheme(context).cardColor,
                    margin: EdgeInsets.only(bottom: 8.h),
                  ),
                  // Shimmer for Second Title Line
                  Container(
                    width: 0.7.sw,
                    height: 16.h,
                    color: myColorScheme(context).cardColor,
                    margin: EdgeInsets.only(bottom: 8.h),
                  ),
                  // Shimmer for Date
                  Container(
                    width: 100.w,
                    height: 12.h,
                    color: myColorScheme(context).cardColor,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class NewsListShimmer extends StatelessWidget {
  final int itemCount;

  const NewsListShimmer({
    super.key,
    this.itemCount = 5,
  });

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: itemCount,
      itemBuilder: (context, index) => const NewsListItemShimmer(),
    );
  }
}
