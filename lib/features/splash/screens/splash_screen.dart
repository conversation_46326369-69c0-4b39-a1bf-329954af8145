import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:sf_app/core/constants/assets.dart';
import 'package:sf_app/core/constants/keys.dart';
import 'package:sf_app/core/routes/routes.dart';
import 'package:sf_app/core/utils/shared_preference_helper.dart';
import 'package:video_player/video_player.dart';
import '../../../core/config/app_config.dart';
import '../../../core/constants/enums.dart';
import '../../../core/shared/logic/country_code/country_code_cubit.dart';
import '../../../core/utils/global.dart' as global;

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with WidgetsBindingObserver {
  VideoPlayerController? _controller;
  bool _isVideoError = false;
  bool _isNavigating = false;
  bool _isVideoInitialized = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeSplash();
  }

  Future<void> _initializeSplash() async {
    context.read<CountryCodeCubit>()
      ..getCountryCode()
      ..getCountries();
    _startFailsafeTimer();

    try {
      _controller = VideoPlayerController.asset(Assets.introVideo);

      await _controller!.initialize();
      if (!mounted) return;

      _controller!
        ..setLooping(false)
        ..setVolume(1.0);

      setState(() => _isVideoInitialized = true);

      await _controller!.play();

      _controller!.addListener(_checkVideoCompletion);
    } catch (error) {
      if (mounted) {
        setState(() => _isVideoError = true);
        _navigateToNextScreen();
      }
    }
  }

  void _startFailsafeTimer() {
    // Failsafe: If video doesn't load/play within 5 seconds, show logo and navigate
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted && !_isVideoInitialized && !_isNavigating) {
        setState(() => _isVideoError = true);
        _navigateToNextScreen();
      }
    });
  }

  void _checkVideoCompletion() {
    if (_controller != null &&
        _controller!.value.isInitialized &&
        _controller!.value.position >= _controller!.value.duration) {
      _navigateToNextScreen();
    }
  }

  Future<void> _navigateToNextScreen() async {
    if (_isNavigating || !mounted) return;
    _isNavigating = true;

    // Clean up video controller early to prevent memory leaks
    await _cleanupVideoController();

    final currentContext = context;

    // // Only reset locale for non-cfroex flavors
    // if (F.appFlavor != Flavor.cfroex && currentContext.mounted) {
    //   currentContext.deleteSaveLocale();
    //   currentContext.resetLocale();
    // }

    if (!currentContext.mounted) return;

    await Future.delayed(const Duration(milliseconds: 200));

    if (!currentContext.mounted) return;

    if (!currentContext.mounted) return;

    final String nextRoute = _determineNextRoute();

    if (currentContext.mounted) {
      Navigator.pushNamedAndRemoveUntil(
        currentContext,
        nextRoute,
        (route) => false,
      );
    }
  }

  String _determineNextRoute() {
    final isLoggedIn = SharedPreferenceHelper().getIsLoggedIn() ?? false;
    final hasWalletPassword = SharedPreferenceHelper()
            .readBoolData(SharedPreferencesKeys.isWalletPasswordSet) ??
        false;
    final isOnboarded = SharedPreferenceHelper().getIsOnBoard() ?? false;

    if (isLoggedIn) {
      return hasWalletPassword ? routeMainScreen : routeOpenWalletScreen;
    }

    if (global.invitationCode != null) {
      return routeRegistrationScreen;
    }

    return isOnboarded ? routeMainScreen : routeOnBoardScreen;
  }

  Future<void> _cleanupVideoController() async {
    if (_controller != null) {
      _controller!.removeListener(_checkVideoCompletion);
      await _controller!.pause();
      await _controller!.dispose();
      _controller = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: WillPopScope(
        onWillPop: () async => false, // Prevent back button during splash
        child: Center(
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 300),
            child: _buildContent(),
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    if (_isVideoError || !_isVideoInitialized) {
      return BlocListener<CountryCodeCubit, CountryCodeState>(
        listenWhen: (previous, current) => previous.status != current.status,
        listener: (context, state) async {
          if (state.status == DataStatus.success) {
            await AppConfig.configureLocale(context, isoCode: state.countryCode?.isoCode);
          }
        },
        child: SvgPicture.asset(
          Assets.logoSvg,
          key: const ValueKey('logo'),
          fit: BoxFit.contain,
          width: 200.w,
          height: 250.h,
        ),
      );
    }

    return _controller != null && _controller!.value.isInitialized
        ? AspectRatio(
            key: const ValueKey('video'),
            aspectRatio: _controller!.value.aspectRatio,
            child: VideoPlayer(_controller!),
          )
        : const SizedBox.shrink();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.resumed) {
      _controller?.play();
    } else if (state == AppLifecycleState.paused) {
      _controller?.pause();
    }
  }

  @override
  void dispose() {
    _cleanupVideoController();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}
