# Flutter Flavor Asset Optimization Scripts

This directory contains scripts to optimize your Flutter app builds by including only the flavor-specific assets (logo and splash) in the final APK/IPA, reducing the app size.

## Quick Start

To build APKs for all flavors with one command:

```bash
./scripts/build_all_flavors_apk.sh
```

To build IPAs for all flavors with one command (macOS only):

```bash
./scripts/build_all_flavors_ipa.sh
```

These scripts will automatically:
1. Detect all available flavors from your project
2. Optimize assets for each flavor
3. Build APKs/IPAs for all flavors
4. Save properly named files to `build/app/outputs/apk/flavors/` or `build/app/outputs/ipa/flavors/`
5. Restore assets when complete

## Available Scripts

### 1. `build_all_flavors_apk.sh`

Builds APKs for all flavors automatically with beautiful progress tracking and asset optimization.

```bash
./scripts/build_all_flavors_apk.sh
```

### 2. `build_all_flavors_ipa.sh`

Builds IPAs for all flavors automatically with beautiful progress tracking and asset optimization. **macOS only** - requires Xcode and iOS development setup.

```bash
./scripts/build_all_flavors_ipa.sh
```

### 3. `pubspec_assets_optimizer.sh`

Modifies the pubspec.yaml file to include only the assets for a specific flavor.

```bash
./scripts/pubspec_assets_optimizer.sh [flavor_name]
```

Example:
```bash
./scripts/pubspec_assets_optimizer.sh ncm
```

### 4. `restore_assets.sh`

Restores all assets in pubspec.yaml while preserving any manual changes you've made.

```bash
./scripts/restore_assets.sh
```

## Integration with Existing Build Scripts

### Option 1: Use the All-in-One Build Script (Recommended)

For building all flavors at once:

**Android APKs:**
```bash
./scripts/build_all_flavors_apk.sh
```

**iOS IPAs (macOS only):**
```bash
./scripts/build_all_flavors_ipa.sh
```

### Option 2: Manual Integration

You can integrate these scripts into your existing build workflow by adding the following lines to your build scripts:

**For Android:**
```bash
# Before building
./scripts/pubspec_assets_optimizer.sh $flavor

# Your existing build commands
flutter build apk --flavor $flavor --release

# After building
./scripts/restore_assets.sh
```

**For iOS:**
```bash
# Before building
./scripts/pubspec_assets_optimizer.sh $flavor

# Your existing build commands
flutter build ipa --flavor $flavor --release

# After building
./scripts/restore_assets.sh
```

## How It Works

### Optimizer Script
- Modifies pubspec.yaml to include only the flavor-specific assets
- Stores the current flavor in a marker file (`.current_flavor`)
- Does not create a backup file that would overwrite your changes

### Restore Script
- Intelligently adds back all flavor-specific assets to pubspec.yaml
- Preserves any manual changes you've made to pubspec.yaml
- Doesn't rely on a backup file that could overwrite your changes
- Cleans up any temporary files created during optimization

This approach reduces the final app size by excluding unnecessary assets from other flavors while preserving your workflow.

## Asset Directory Structure

The scripts expect your assets to be organized in the following structure:

```
assets/
├── logo/
│   ├── ncm/
│   │   └── logo.png
│   ├── cfroex/
│   │   └── logo.png
│   └── sf_app/
│       └── logo.png
└── splash/
    ├── ncm/
    │   └── introVideo.mp4
    ├── cforex/
    │   └── introVideo.mp4
    └── sf_app/
        └── introVideo.mp4
```

## Recommended Workflow

1. Run the optimizer script before building:
   ```bash
   ./scripts/pubspec_assets_optimizer.sh ncm
   ```

2. Build your app as usual:
   ```bash
   flutter build apk --flavor ncm
   ```

3. Make any changes to pubspec.yaml if needed during development

4. When you're done, restore all assets:
   ```bash
   ./scripts/restore_assets.sh
   ```

This workflow ensures that only the necessary assets are included in your build while allowing you to make changes to pubspec.yaml without losing them during restoration.

## Troubleshooting

### Common Issues with `build_all_flavors_apk.sh`

**1. "fvm command not found"**
```bash
# Install FVM
dart pub global activate fvm
# Add to PATH if needed
export PATH="$PATH":"$HOME/.pub-cache/bin"
```

**2. "No flavors found"**
- Ensure `lib/flavors.dart` exists with a proper `enum Flavor` definition
- Check that flavors are properly defined in the enum

**3. "Asset optimization failed"**
- Check that `pubspec.yaml` exists and is writable
- Verify flavor-specific asset directories exist in `assets/logo/[flavor]/` and `assets/splash/[flavor]/`

**4. "APK build failed"**
- Check the build log files: `build_[flavor].log`
- Ensure Android SDK is properly configured
- Verify signing configuration in `android/app/build.gradle`

**5. "APK not found"**
- Check if the build actually succeeded by looking at the logs
- Verify the expected APK path: `build/app/outputs/flutter-apk/app-[flavor]-release.apk`

### Common Issues with `build_all_flavors_ipa.sh`

**1. "iOS builds are only supported on macOS"**
- IPA builds require macOS with Xcode installed
- Use the APK script on other platforms

**2. "xcodebuild command not found"**
```bash
# Install Xcode from App Store, then install command line tools
xcode-select --install
```

**3. "No provisioning profile found"**
- Ensure you have a valid Apple Developer account
- Configure signing in Xcode or through `ios/Runner.xcodeproj`
- Check that provisioning profiles are properly set up

**4. "IPA build failed"**
- Check the build log files: `build_[flavor]_ios.log`
- Verify iOS deployment target compatibility
- Ensure all iOS dependencies are properly configured

**5. "IPA not found"**
- Check if the build actually succeeded by looking at the logs
- Verify the expected IPA path: `build/ios/ipa/Runner.ipa`
- Ensure code signing is properly configured

### Build Logs

Each flavor build creates a detailed log file:

**Android APK builds:**
- `build_sf_app.log`
- `build_cfroex.log`
- `build_ncm.log`
- `build_sis.log`

**iOS IPA builds:**
- `build_sf_app_ios.log`
- `build_cfroex_ios.log`
- `build_ncm_ios.log`
- `build_sis_ios.log`

Check these files for specific error details if a build fails.
